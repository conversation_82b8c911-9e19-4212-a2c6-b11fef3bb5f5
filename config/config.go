package config

import (
	"log"
	"os"
	"strconv"
)

// Config holds all the configuration for the application
type Config struct {
	OllamaAPIBaseURL     string
	OllamaEmbeddingModel string
	// REMOVED: OllamaRerankerModel
	OllamaGenerateModel  string
	QdrantGRPCURL        string
	QdrantCollectionName string
	VectorSize           uint64
}

// Load loads configuration from environment variables
func Load() *Config {
	vectorSizeStr := getEnv("VECTOR_SIZE", "1024")
	vectorSize, err := strconv.ParseUint(vectorSizeStr, 10, 64)
	if err != nil {
		log.Fatalf("Invalid VECTOR_SIZE: %v", err)
	}

	return &Config{
		OllamaAPIBaseURL:     getEnv("OLLAMA_API_BASE_URL", "http://localhost:11434"),
		OllamaEmbeddingModel: getEnv("OLLAMA_EMBEDDING_MODEL", "qwen-embedding"),
		// REMOVED: Reranker model configuration
		OllamaGenerateModel:  getEnv("OLLAMA_GENERATE_MODEL", "qwen3:1.7b"),
		QdrantGRPCURL:        getEnv("QDRANT_GRPC_URL", "localhost:6334"),
		QdrantCollectionName: getEnv("QDRANT_COLLECTION_NAME", "rag_pipeline_go"),
		VectorSize:           vectorSize,
	}
}

// getEnv retrieves an environment variable or returns a default value
func getEnv(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	// log.Printf("Using default value for %s", key)
	return defaultValue
}
