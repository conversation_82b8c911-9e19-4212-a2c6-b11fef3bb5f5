// internal/ollama/ollama.go
package ollama

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/denissud/rag-pipeline-go/config"
)

// Client is a client for interacting with the Ollama API
type Client struct {
	httpClient *http.Client
	cfg        *config.Config
	embeddingModel  string
	// REMOVED: rerankerModel
	generationModel string
}

// NewClient creates a new Ollama API client
func NewClient(cfg *config.Config) *Client {
	return &Client{
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		cfg:             cfg,
		embeddingModel:  cfg.OllamaEmbeddingModel,
		// REMOVED: rerankerModel assignment
		generationModel: cfg.OllamaGenerateModel,
	}
}

// --- GetEmbedding function remains the same ---
// OllamaEmbeddingRequest defines the structure for the embedding API request
type OllamaEmbeddingRequest struct {
	Model  string `json:"model"`
	Prompt string `json:"prompt"`
}

// OllamaEmbeddingResponse defines the structure for the embedding API response
type OllamaEmbeddingResponse struct {
	Embedding []float32 `json:"embedding"`
}

// GetEmbedding sends text to Ollama and returns its vector embedding
func (c *Client) GetEmbedding(ctx context.Context, text string) ([]float32, error) {
	reqBody, err := json.Marshal(OllamaEmbeddingRequest{
		Model:  c.embeddingModel,
		Prompt: text,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to marshal embedding request: %w", err)
	}

	apiURL := fmt.Sprintf("%s/api/embeddings", c.cfg.OllamaAPIBaseURL)
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, apiURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create embedding request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send embedding request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("received non-OK HTTP status for embedding: %s, body: %s", resp.Status, string(bodyBytes))
	}

	var embeddingResp OllamaEmbeddingResponse
	if err := json.NewDecoder(resp.Body).Decode(&embeddingResp); err != nil {
		return nil, fmt.Errorf("failed to decode embedding response: %w", err)
	}

	return embeddingResp.Embedding, nil
}


// --- GenerateCompletion and its related structs remain the same ---
// OllamaGenerateRequest defines the structure for the generation API request
type OllamaGenerateRequest struct {
	Model  string `json:"model"`
	Prompt string `json:"prompt"`
	Stream bool   `json:"stream"`
	Think bool `json:"think"`
}

// OllamaGenerateResponse defines the structure for the generation API response
type OllamaGenerateResponse struct {
	Response string `json:"response"`
}

// GenerateCompletion sends a context and query to the generation model to get a final answer
func (c *Client) GenerateCompletion(ctx context.Context, contextText, query string) (string, error) {
	prompt := fmt.Sprintf("Based ONLY on the following context, answer the user's query.\n\nContext:\n%s\n\nQuery: %s\n\nAnswer:", contextText, query)

	reqBody, err := json.Marshal(OllamaGenerateRequest{
		Model:  c.generationModel,
		Prompt: prompt,
		Stream: false,
		Think: false,
	})
	if err != nil {
		return "", fmt.Errorf("failed to marshal completion request: %w", err)
	}

	apiURL := fmt.Sprintf("%s/api/generate", c.cfg.OllamaAPIBaseURL)
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, apiURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return "", fmt.Errorf("failed to create completion request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send completion request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("received non-OK HTTP status for completion: %s, body: %s", resp.Status, string(bodyBytes))
	}

	var completionResp OllamaGenerateResponse
	if err := json.NewDecoder(resp.Body).Decode(&completionResp); err != nil {
		return "", fmt.Errorf("failed to decode completion response: %w", err)
	}

	return completionResp.Response, nil
}

// REMOVED: The entire Rerank function is gone.
