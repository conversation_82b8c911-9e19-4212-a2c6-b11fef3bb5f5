package qdrant

import (
	"context"
	"fmt"
	"log"

	"github.com/denissud/rag-pipeline-go/config"

	"github.com/qdrant/go-client/qdrant"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// DocumentChunk represents a piece of text to be stored in Qdrant
type DocumentChunk struct {
	ID      string
	Content string
	Source  string
}

// Client manages interactions with the Qdrant database
type Client struct {
	qdrantClient     qdrant.PointsClient
	collectionName   string
	vectorSize       uint64
}

// NewClient creates a new Qdrant client and ensures the collection exists
func NewClient(ctx context.Context, cfg *config.Config) (*Client, error) {
	conn, err := grpc.Dial(cfg.QdrantGRPCURL, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, fmt.Errorf("could not connect to Qdrant: %w", err)
	}

	collectionsClient := qdrant.NewCollectionsClient(conn)
	
	// Check if collection exists
	res, err := collectionsClient.Get(ctx, &qdrant.GetCollectionInfoRequest{
		CollectionName: cfg.QdrantCollectionName,
	})
	
	if err != nil { // A gRPC error could mean the collection does not exist
		log.Printf("Collection '%s' may not exist, attempting to create it. Error: %v", cfg.QdrantCollectionName, err)
		
		_, createErr := collectionsClient.Create(ctx, &qdrant.CreateCollection{
			CollectionName: cfg.QdrantCollectionName,
			VectorsConfig: &qdrant.VectorsConfig{
				Config: &qdrant.VectorsConfig_Params{
					Params: &qdrant.VectorParams{
						Size:     cfg.VectorSize,
						Distance: qdrant.Distance_Cosine,
					},
				},
			},
		})
		if createErr != nil {
			return nil, fmt.Errorf("could not create collection '%s': %w", cfg.QdrantCollectionName, createErr)
		}
		log.Printf("Successfully created collection '%s'", cfg.QdrantCollectionName)
	} else {
		log.Printf("Collection '%s' already exists. Details: %v", cfg.QdrantCollectionName, res.GetResult())
	}


	return &Client{
		qdrantClient:     qdrant.NewPointsClient(conn),
		collectionName:   cfg.QdrantCollectionName,
		vectorSize:       cfg.VectorSize,
	}, nil
}

// UpsertPoints inserts or updates document chunks and their embeddings
func (c *Client) UpsertPoints(ctx context.Context, chunks []DocumentChunk, embeddings [][]float32) error {
	if len(chunks) != len(embeddings) {
		return fmt.Errorf("number of chunks (%d) and embeddings (%d) must be equal", len(chunks), len(embeddings))
	}

	points := make([]*qdrant.PointStruct, len(chunks))
	for i, chunk := range chunks {
		points[i] = &qdrant.PointStruct{
			Id: &qdrant.PointId{
				PointIdOptions: &qdrant.PointId_Uuid{
					Uuid: chunk.ID,
				},
			},
			Payload: map[string]*qdrant.Value{
				"content": {
					Kind: &qdrant.Value_StringValue{StringValue: chunk.Content},
				},
				"source": {
					Kind: &qdrant.Value_StringValue{StringValue: chunk.Source},
				},
			},
			Vectors: &qdrant.Vectors{
				VectorsOptions: &qdrant.Vectors_Vector{
					Vector: &qdrant.Vector{
						Data: embeddings[i],
					},
				},
			},
		}
	}

	waitUpsert := true
	_, err := c.qdrantClient.Upsert(ctx, &qdrant.UpsertPoints{
		CollectionName: c.collectionName,
		Points:         points,
		Wait:           &waitUpsert,
	})

	if err != nil {
		return fmt.Errorf("could not upsert points: %w", err)
	}

	// log.Printf("Successfully upserted %d points.", len(points))
	return nil
}

// Search performs a similarity search in the Qdrant collection
func (c *Client) Search(ctx context.Context, queryEmbedding []float32, limit uint64) ([]*qdrant.ScoredPoint, error) {
	withPayload := true
	withVectors := false

	searchRequest := &qdrant.SearchPoints{
		CollectionName: c.collectionName,
		Vector:         queryEmbedding,
		Limit:          limit,
		WithPayload: &qdrant.WithPayloadSelector{
			SelectorOptions: &qdrant.WithPayloadSelector_Enable{
				Enable: withPayload,
			},
		},
		WithVectors: &qdrant.WithVectorsSelector{
			SelectorOptions: &qdrant.WithVectorsSelector_Enable{
				Enable: withVectors,
			},
		},
	}

	searchResult, err := c.qdrantClient.Search(ctx, searchRequest)
	if err != nil {
		return nil, fmt.Errorf("could not perform search: %w", err)
	}

	return searchResult.GetResult(), nil
}
