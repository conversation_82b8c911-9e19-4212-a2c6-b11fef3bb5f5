// internal/rag/pipeline.go
package rag

import (
	"context"
	"fmt"
	"strings"

	"github.com/denissud/rag-pipeline-go/internal/ollama"
	"github.com/denissud/rag-pipeline-go/internal/qdrant"

	"github.com/google/uuid"
)

// RAGPipeline orchestrates the RAG process
type RAGPipeline struct {
	ollamaClient *ollama.Client
	qdrantClient *qdrant.Client
	chunkSize    int
	chunkOverlap int
	topK         uint64 // Number of documents to retrieve and use for context
	// REMOVED: topN parameter
}

// NewRAGPipeline creates a new RAG pipeline
func NewRAGPipeline(ollamaClient *ollama.Client, qdrantClient *qdrant.Client, chunkSize, chunkOverlap int, topK uint64) *RAGPipeline {
	return &RAGPipeline{
		ollamaClient: ollamaClient,
		qdrantClient: qdrantClient,
		chunkSize:    chunkSize,
		chunkOverlap: chunkOverlap,
		topK:         topK,
	}
}

// --- IngestDocument and splitContent functions remain exactly the same ---
// IngestDocument splits a document, generates embeddings, and stores them
func (p *RAGPipeline) IngestDocument(ctx context.Context, content, source string) error {
	// log.Printf("Starting ingestion for source: %s", source)

	// 1. Document Splitting
	chunks := p.splitContent(content, source)
	if len(chunks) == 0 {
		return fmt.Errorf("no chunks were created from the content of source: %s", source)
	}

	// log.Printf("Split content into %d chunks.", len(chunks))

	// 2. Embedding
	chunkContents := make([]string, len(chunks))
	for i, chunk := range chunks {
		chunkContents[i] = chunk.Content
	}

	embeddings := make([][]float32, len(chunks))
	for i, chunkContent := range chunkContents {
		embedding, err := p.ollamaClient.GetEmbedding(ctx, chunkContent)
		if err != nil {
			return fmt.Errorf("failed to generate embedding for chunk %d: %w", i, err)
		}
		embeddings[i] = embedding
		// log.Printf("Generated embedding for chunk %d of %d", i+1, len(chunks))
	}

	// 3. Storage
	err := p.qdrantClient.UpsertPoints(ctx, chunks, embeddings)
	if err != nil {
		return fmt.Errorf("failed to upsert document chunks: %w", err)
	}

	// log.Printf("Successfully ingested document from source: %s", source)
	return nil
}

// splitContent performs simple character-based splitting
func (p *RAGPipeline) splitContent(content, source string) []qdrant.DocumentChunk {
	var chunks []qdrant.DocumentChunk
	step := p.chunkSize - p.chunkOverlap
	for i := 0; i < len(content); i += step {
		end := i + p.chunkSize
		if end > len(content) {
			end = len(content)
		}
		chunkContent := content[i:end]
		if strings.TrimSpace(chunkContent) == "" {
			continue
		}
		chunks = append(chunks, qdrant.DocumentChunk{
			ID:      uuid.New().String(),
			Content: chunkContent,
			Source:  source,
		})
		if end == len(content) {
			break
		}
	}
	return chunks
}


// RAGQuery executes the full RAG process for a given query (SIMPLIFIED)
func (p *RAGPipeline) RAGQuery(ctx context.Context, query string) (string, error) {
	// log.Printf("Executing simplified RAG query: '%s'", query)

	// 1. Query Embedding
	// log.Println("Generating query embedding...")
	queryEmbedding, err := p.ollamaClient.GetEmbedding(ctx, query)
	if err != nil {
		return "", fmt.Errorf("failed to embed query: %w", err)
	}

	// 2. Retrieval
	// log.Printf("Retrieving top %d documents...", p.topK)
	retrievedDocs, err := p.qdrantClient.Search(ctx, queryEmbedding, p.topK)
	if err != nil {
		return "", fmt.Errorf("failed to retrieve documents: %w", err)
	}
	if len(retrievedDocs) == 0 {
		return "I could not find any relevant information in the knowledge base to answer your query.", nil
	}
	// log.Printf("Retrieved %d documents.", len(retrievedDocs))

	// 3. Context Formulation (No Reranking)
	// log.Printf("Formulating context from top %d retrieved documents...", len(retrievedDocs))
	var contextBuilder strings.Builder
	for _, doc := range retrievedDocs {
		content := doc.Payload["content"].GetStringValue()
		source := doc.Payload["source"].GetStringValue()
		contextBuilder.WriteString(fmt.Sprintf("Source: %s\nContent: %s\n\n", source, content))
	}
	contextText := contextBuilder.String()

	// 4. Generation
	// log.Println("Generating final answer...")
	answer, err := p.ollamaClient.GenerateCompletion(ctx, contextText, query)
	if err != nil {
		return "", fmt.Errorf("failed to generate completion: %w", err)
	}

	return answer, nil
}
