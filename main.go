// main.go
package main

import (
	"bufio"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/denissud/rag-pipeline-go/config"
	"github.com/denissud/rag-pipeline-go/internal/ollama"
	"github.com/denissud/rag-pipeline-go/internal/qdrant"
	"github.com/denissud/rag-pipeline-go/internal/rag"
)

type CLIConfig struct {
	DocsDir    string
	ChunkSize  int
	Overlap    int
	TopK       int
	Debug      bool
	Quiet      bool
	Timeout    time.Duration
}

func printUsage() {
	fmt.Printf(`RAG Pipeline CLI Tool

Usage: %s <command> [options]

Commands:
  ingest <directory>    Ingest documents from directory
  query <question>      Ask a question
  chat                  Start interactive chat mode
  test                  Run performance tests
  status                Check system status

Global Options:
  -debug               Enable debug mode
  -quiet               Suppress non-essential output
  -chunk-size int      Document chunk size (default 1024)
  -overlap int         Chunk overlap size (default 128)
  -top-k int           Number of documents to retrieve (default 5)
  -timeout duration    Operation timeout (default 5m)

Examples:
  %s ingest ./docs
  %s query "What is a RAG pipeline?"
  %s chat
  %s test -debug
  %s status

Environment Variables:
  DEBUG_MODE           Enable debug mode (true/false)
  OLLAMA_API_BASE_URL  Ollama API URL (default: http://localhost:11434)
  QDRANT_GRPC_URL      Qdrant gRPC URL (default: localhost:6334)

`, os.Args[0], os.Args[0], os.Args[0], os.Args[0], os.Args[0], os.Args[0])
}

func parseFlags() *CLIConfig {
	config := &CLIConfig{
		ChunkSize: 1024,
		Overlap:   128,
		TopK:      5,
		Timeout:   5 * time.Minute,
	}

	flag.BoolVar(&config.Debug, "debug", false, "Enable debug mode")
	flag.BoolVar(&config.Quiet, "quiet", false, "Suppress non-essential output")
	flag.IntVar(&config.ChunkSize, "chunk-size", 1024, "Document chunk size")
	flag.IntVar(&config.Overlap, "overlap", 128, "Chunk overlap size")
	flag.IntVar(&config.TopK, "top-k", 5, "Number of documents to retrieve")
	flag.DurationVar(&config.Timeout, "timeout", 5*time.Minute, "Operation timeout")

	flag.Usage = printUsage
	flag.Parse()

	return config
}

func initializeRAGPipeline(ctx context.Context, cliConfig *CLIConfig) (*rag.RAGPipeline, *config.Config, error) {
	cfg := config.Load()

	// Override debug mode if specified via CLI
	if cliConfig.Debug {
		cfg.DebugMode = true
	}

	if !cliConfig.Quiet {
		fmt.Printf("Initializing RAG pipeline...\n")
		if cfg.DebugMode {
			fmt.Printf("Debug mode: enabled\n")
		}
	}

	ollamaClient := ollama.NewClient(cfg)
	qdrantClient, err := qdrant.NewClient(ctx, cfg)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to initialize Qdrant client: %w", err)
	}

	ragPipeline := rag.NewRAGPipeline(
		ollamaClient,
		qdrantClient,
		cfg,
		cliConfig.ChunkSize,
		cliConfig.Overlap,
		uint64(cliConfig.TopK),
	)

	if !cliConfig.Quiet {
		fmt.Printf("RAG pipeline initialized successfully\n")
	}

	return ragPipeline, cfg, nil
}

func main() {
	cliConfig := parseFlags()

	if len(os.Args) < 2 {
		printUsage()
		os.Exit(1)
	}

	command := os.Args[1]

	// Handle help command
	if command == "help" || command == "-h" || command == "--help" {
		printUsage()
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), cliConfig.Timeout)
	defer cancel()

	switch command {
	case "ingest":
		handleIngestCommand(ctx, cliConfig)
	case "query":
		handleQueryCommand(ctx, cliConfig)
	case "chat":
		handleChatCommand(ctx, cliConfig)
	case "test":
		handleTestCommand(ctx, cliConfig)
	case "status":
		handleStatusCommand(ctx, cliConfig)
	default:
		fmt.Printf("Unknown command: %s\n\n", command)
		printUsage()
		os.Exit(1)
	}
}

func handleIngestCommand(ctx context.Context, cliConfig *CLIConfig) {
	if len(os.Args) < 3 {
		fmt.Printf("Usage: %s ingest <directory>\n", os.Args[0])
		os.Exit(1)
	}

	docsDir := os.Args[2]

	if _, err := os.Stat(docsDir); os.IsNotExist(err) {
		fmt.Printf("Error: Directory '%s' does not exist\n", docsDir)
		os.Exit(1)
	}

	ragPipeline, cfg, err := initializeRAGPipeline(ctx, cliConfig)
	if err != nil {
		log.Fatalf("Failed to initialize RAG pipeline: %v", err)
	}

	if !cliConfig.Quiet {
		fmt.Printf("Ingesting documents from: %s\n", docsDir)
	}

	files, err := os.ReadDir(docsDir)
	if err != nil {
		log.Fatalf("Failed to read directory %s: %v", docsDir, err)
	}

	ingestedCount := 0
	for _, file := range files {
		if !file.IsDir() && filepath.Ext(file.Name()) == ".md" {
			filePath := filepath.Join(docsDir, file.Name())
			content, err := os.ReadFile(filePath)
			if err != nil {
				log.Printf("Failed to read file %s: %v", filePath, err)
				continue
			}

			if cfg.DebugMode {
				result, err := ragPipeline.IngestDocumentWithDebug(ctx, string(content), filePath)
				if err != nil {
					log.Printf("Failed to ingest document %s: %v", filePath, err)
				} else {
					ingestedCount++
					if !cliConfig.Quiet && result.Debug != nil {
						fmt.Printf("✓ %s - Chunks: %d, Time: %v\n",
							filepath.Base(filePath), result.Debug.ChunksCreated, result.Debug.TotalTime)
					}
				}
			} else {
				err = ragPipeline.IngestDocument(ctx, string(content), filePath)
				if err != nil {
					log.Printf("Failed to ingest document %s: %v", filePath, err)
				} else {
					ingestedCount++
					if !cliConfig.Quiet {
						fmt.Printf("✓ %s\n", filepath.Base(filePath))
					}
				}
			}
		}
	}

	if !cliConfig.Quiet {
		fmt.Printf("\nIngestion complete: %d documents processed\n", ingestedCount)
	}
}

func handleQueryCommand(ctx context.Context, cliConfig *CLIConfig) {
	if len(os.Args) < 3 {
		fmt.Printf("Usage: %s query \"<question>\"\n", os.Args[0])
		os.Exit(1)
	}

	query := strings.Join(os.Args[2:], " ")

	ragPipeline, cfg, err := initializeRAGPipeline(ctx, cliConfig)
	if err != nil {
		log.Fatalf("Failed to initialize RAG pipeline: %v", err)
	}

	if !cliConfig.Quiet {
		fmt.Printf("Query: %s\n\n", query)
	}

	if cfg.DebugMode {
		result, err := ragPipeline.RAGQueryWithDebug(ctx, query)
		if err != nil {
			log.Fatalf("Error processing query: %v", err)
		}

		fmt.Printf("Answer: %s\n", result.Answer)

		// Always show cited sources (just filenames)
		if result.Debug != nil && len(result.Debug.RetrievedSources) > 0 {
			fmt.Printf("\nSources: ")
			for i, source := range result.Debug.RetrievedSources {
				if i > 0 {
					fmt.Printf(", ")
				}
				fmt.Printf("%s", filepath.Base(source))
			}
			fmt.Printf("\n")
		}

		if result.Debug != nil && !cliConfig.Quiet {
			fmt.Printf("\nDebug Information:\n")
			fmt.Printf("- Processing time: %v\n", result.Debug.TotalTime)
			fmt.Printf("- Documents retrieved: %d\n", result.Debug.RetrievedDocsCount)

			if cliConfig.Debug {
				fmt.Printf("\nFull Debug Info:\n")
				debugJSON, _ := json.MarshalIndent(result.Debug, "", "  ")
				fmt.Printf("%s\n", string(debugJSON))
			}
		}
	} else {
		answer, err := ragPipeline.RAGQuery(ctx, query)
		if err != nil {
			log.Fatalf("Error processing query: %v", err)
		}
		fmt.Printf("Answer: %s\n", answer)
	}
}

func handleChatCommand(ctx context.Context, cliConfig *CLIConfig) {
	ragPipeline, cfg, err := initializeRAGPipeline(ctx, cliConfig)
	if err != nil {
		log.Fatalf("Failed to initialize RAG pipeline: %v", err)
	}

	fmt.Println("RAG Pipeline Chat Mode")
	fmt.Println("Type 'exit' or 'quit' to end the session")
	fmt.Println("Type 'help' for available commands")
	fmt.Println("=====================================")

	scanner := bufio.NewScanner(os.Stdin)

	for {
		fmt.Print("\n> ")
		if !scanner.Scan() {
			break
		}

		input := strings.TrimSpace(scanner.Text())
		if input == "" {
			continue
		}

		switch input {
		case "exit", "quit":
			fmt.Println("Goodbye!")
			return
		case "help":
			fmt.Println("Available commands:")
			fmt.Println("  help    - Show this help message")
			fmt.Println("  exit    - Exit chat mode")
			fmt.Println("  quit    - Exit chat mode")
			fmt.Println("  <question> - Ask any question")
			continue
		}

		// Process the query
		queryCtx, cancel := context.WithTimeout(ctx, 2*time.Minute)

		if cfg.DebugMode {
			result, err := ragPipeline.RAGQueryWithDebug(queryCtx, input)
			if err != nil {
				fmt.Printf("Error: %v\n", err)
			} else {
				fmt.Printf("\nAnswer: %s\n", result.Answer)

				// Always show cited sources (just filenames)
				if result.Debug != nil && len(result.Debug.RetrievedSources) > 0 {
					fmt.Printf("Sources: ")
					for i, source := range result.Debug.RetrievedSources {
						if i > 0 {
							fmt.Printf(", ")
						}
						fmt.Printf("%s", filepath.Base(source))
					}
					fmt.Printf("\n")
				}

				if result.Debug != nil && !cliConfig.Quiet {
					fmt.Printf("(Processed in %v using %d documents)\n",
						result.Debug.TotalTime, result.Debug.RetrievedDocsCount)
				}
			}
		} else {
			answer, err := ragPipeline.RAGQuery(queryCtx, input)
			if err != nil {
				fmt.Printf("Error: %v\n", err)
			} else {
				fmt.Printf("\nAnswer: %s\n", answer)
			}
		}

		cancel()
	}
}

func handleTestCommand(ctx context.Context, cliConfig *CLIConfig) {
	// Force debug mode for testing
	cliConfig.Debug = true

	ragPipeline, _, err := initializeRAGPipeline(ctx, cliConfig)
	if err != nil {
		log.Fatalf("Failed to initialize RAG pipeline: %v", err)
	}

	// Check if docs directory exists
	docsDir := "./docs"
	if _, err := os.Stat(docsDir); os.IsNotExist(err) {
		fmt.Printf("Error: ./docs directory not found. Please ingest documents first.\n")
		fmt.Printf("Usage: %s ingest ./docs\n", os.Args[0])
		os.Exit(1)
	}

	testQueries := []string{
		"What is a RAG pipeline?",
		"What is Qwen3-Embedding-0.6B?",
		"How do you use Go with Nix?",
		"What are the benefits of using Go?",
		"What is Qdrant?",
	}

	fmt.Printf("Running performance test with %d queries...\n\n", len(testQueries))

	var totalTime time.Duration
	successCount := 0

	for i, query := range testQueries {
		fmt.Printf("[%d/%d] %s\n", i+1, len(testQueries), query)

		queryCtx, cancel := context.WithTimeout(ctx, 2*time.Minute)
		result, err := ragPipeline.RAGQueryWithDebug(queryCtx, query)
		cancel()

		if err != nil {
			fmt.Printf("❌ Error: %v\n\n", err)
			continue
		}

		successCount++
		if result.Debug != nil {
			totalTime += result.Debug.TotalTime
			fmt.Printf("✓ Answer: %s\n", result.Answer)
			fmt.Printf("  Time: %v, Docs: %d, Sources: %v\n\n",
				result.Debug.TotalTime,
				result.Debug.RetrievedDocsCount,
				result.Debug.RetrievedSources)
		}
	}

	fmt.Printf("Test Results:\n")
	fmt.Printf("- Queries processed: %d/%d\n", successCount, len(testQueries))
	if successCount > 0 {
		avgTime := totalTime / time.Duration(successCount)
		fmt.Printf("- Average query time: %v\n", avgTime)
	}
	fmt.Printf("- Total test time: %v\n", totalTime)
}

func handleStatusCommand(ctx context.Context, cliConfig *CLIConfig) {
	fmt.Println("RAG Pipeline Status Check")
	fmt.Println("========================")

	cfg := config.Load()

	// Check Ollama
	fmt.Printf("Ollama API (%s): ", cfg.OllamaAPIBaseURL)
	ollamaClient := ollama.NewClient(cfg)

	// Try a simple embedding request
	testCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	_, err := ollamaClient.GetEmbedding(testCtx, "test")
	cancel()

	if err != nil {
		fmt.Printf("❌ Failed (%v)\n", err)
	} else {
		fmt.Printf("✓ Connected\n")
	}

	// Check Qdrant
	fmt.Printf("Qdrant (%s): ", cfg.QdrantGRPCURL)
	testCtx, cancel = context.WithTimeout(ctx, 10*time.Second)
	_, err = qdrant.NewClient(testCtx, cfg)
	cancel()

	if err != nil {
		fmt.Printf("❌ Failed (%v)\n", err)
	} else {
		fmt.Printf("✓ Connected\n")
	}

	// Check configuration
	fmt.Printf("\nConfiguration:\n")
	fmt.Printf("- Embedding Model: %s\n", cfg.OllamaEmbeddingModel)
	fmt.Printf("- Generation Model: %s\n", cfg.OllamaGenerateModel)
	fmt.Printf("- Collection: %s\n", cfg.QdrantCollectionName)
	fmt.Printf("- Vector Size: %d\n", cfg.VectorSize)
	fmt.Printf("- Debug Mode: %v\n", cfg.DebugMode)

	// Check docs directory
	fmt.Printf("\nDocument Directory:\n")
	docsDir := "./docs"
	if files, err := os.ReadDir(docsDir); err != nil {
		fmt.Printf("- ./docs: ❌ Not accessible (%v)\n", err)
	} else {
		mdCount := 0
		for _, file := range files {
			if !file.IsDir() && filepath.Ext(file.Name()) == ".md" {
				mdCount++
			}
		}
		fmt.Printf("- ./docs: ✓ %d markdown files found\n", mdCount)
	}
}
