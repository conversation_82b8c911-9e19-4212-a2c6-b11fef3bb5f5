// main.go
package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/denissud/rag-pipeline-go/config"
	"github.com/denissud/rag-pipeline-go/internal/ollama"
	"github.com/denissud/rag-pipeline-go/internal/qdrant"
	"github.com/denissud/rag-pipeline-go/internal/rag"
)

func main() {
	// 1. Load Configuration
	cfg := config.Load()
	log.Println("Configuration loaded.")

	// Set a global context with a timeout for the entire process
	mainCtx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// 2. Initialize Clients
	ollamaClient := ollama.NewClient(cfg)
	log.Println("Ollama client initialized.")

	qdrantClient, err := qdrant.NewClient(mainCtx, cfg)
	if err != nil {
		log.Fatalf("Failed to initialize Qdrant client: %v", err)
	}
	log.Println("Qdrant client initialized and collection is ready.")

	// 3. Initialize Simplified RAG Pipeline
	// UPDATED: No longer passing topN parameter
	ragPipeline := rag.NewRAGPipeline(
		ollamaClient,
		qdrantClient,
		cfg,   // Pass config for debug mode
		1024, // Chunk Size
		128,  // Chunk Overlap
		1,    // Top K for retrieval (we'll use the top 5 documents for context)
	)

	// 4. Demonstrate Document Ingestion
	docsDir := "./docs"
	ingestDocuments(mainCtx, ragPipeline, docsDir, cfg)

	// 5. Demonstrate Query Handling
	queries := []string{
		"What is a RAG pipeline?",
		"What is Qwen3-Embedding-0.6B?",
		"How do you use Go with Nix?",
	}


	for _, query := range queries {
		fmt.Printf("\n--- Query: %s ---\n", query)

		queryCtx, queryCancel := context.WithTimeout(mainCtx, 2*time.Minute)

		if cfg.DebugMode {
			// Use debug version when debug mode is enabled
			result, err := ragPipeline.RAGQueryWithDebug(queryCtx, query)
			if err != nil {
				log.Printf("Error processing query '%s': %v", query, err)
			} else {
				fmt.Printf("Answer: %s\n", result.Answer)
				if result.Debug != nil {
					fmt.Println("\n=== DEBUG INFORMATION ===")
					debugJSON, _ := json.MarshalIndent(result.Debug, "", "  ")
					fmt.Printf("Debug Info: %s\n", string(debugJSON))
				}
			}
		} else {
			// Use regular version when debug mode is disabled
			answer, err := ragPipeline.RAGQuery(queryCtx, query)
			if err != nil {
				log.Printf("Error processing query '%s': %v", query, err)
			} else {
				fmt.Printf("Answer: %s\n", answer)
			}
		}
		queryCancel()
	}

	fmt.Println("\n=====================================")
	fmt.Println("          RAG DEMO COMPLETE")
	fmt.Println("=====================================")
}

// --- ingestDocuments function updated to support debug mode ---
// ingestDocuments reads all markdown files from a directory and ingests them.
func ingestDocuments(ctx context.Context, p *rag.RAGPipeline, dir string, cfg *config.Config) {
	files, err := os.ReadDir(dir)
	if err != nil {
		log.Fatalf("Failed to read directory %s: %v", dir, err)
	}

	for _, file := range files {
		if !file.IsDir() && filepath.Ext(file.Name()) == ".md" {
			filePath := filepath.Join(dir, file.Name())
			content, err := os.ReadFile(filePath)
			if err != nil {
				log.Printf("Failed to read file %s: %v", filePath, err)
				continue
			}

			if cfg.DebugMode {
				// Use debug version when debug mode is enabled
				result, err := p.IngestDocumentWithDebug(ctx, string(content), filePath)
				if err != nil {
					log.Printf("Failed to ingest document %s: %v", filePath, err)
				} else if result.Debug != nil {
					fmt.Printf("Ingested %s - Chunks: %d, Time: %v\n",
						filePath, result.Debug.ChunksCreated, result.Debug.TotalTime)
				}
			} else {
				// Use regular version when debug mode is disabled
				err = p.IngestDocument(ctx, string(content), filePath)
				if err != nil {
					log.Printf("Failed to ingest document %s: %v", filePath, err)
				}
			}
		}
	}
}
