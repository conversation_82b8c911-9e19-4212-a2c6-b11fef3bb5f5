// test_debug.go - Simple test to demonstrate debug functionality
package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/denissud/rag-pipeline-go/config"
	"github.com/denissud/rag-pipeline-go/internal/ollama"
	"github.com/denissud/rag-pipeline-go/internal/qdrant"
	"github.com/denissud/rag-pipeline-go/internal/rag"
)

func testDebugMode() {
	fmt.Println("=== Testing Debug Mode ===")
	
	// Create a config with debug mode enabled
	cfg := &config.Config{
		OllamaAPIBaseURL:     "http://localhost:11434",
		OllamaEmbeddingModel: "qwen-embedding",
		OllamaGenerateModel:  "qwen3:1.7b",
		QdrantGRPCURL:        "localhost:6334",
		QdrantCollectionName: "rag_pipeline_go_debug_test",
		VectorSize:           1024,
		DebugMode:            true,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()

	// Initialize clients
	ollamaClient := ollama.NewClient(cfg)
	qdrantClient, err := qdrant.NewClient(ctx, cfg)
	if err != nil {
		log.Fatalf("Failed to initialize Qdrant client: %v", err)
	}

	// Initialize RAG pipeline
	ragPipeline := rag.NewRAGPipeline(
		ollamaClient,
		qdrantClient,
		cfg,
		512, // Smaller chunk size for testing
		64,  // Smaller overlap for testing
		2,   // Top K for retrieval
	)

	// Test document ingestion with debug
	fmt.Println("\n--- Testing Document Ingestion with Debug ---")
	testContent := `
# Test Document

This is a test document for demonstrating debug functionality.
It contains information about RAG pipelines and vector databases.

RAG (Retrieval-Augmented Generation) is a technique that combines 
information retrieval with text generation to provide more accurate 
and contextual responses.

Vector databases like Qdrant store embeddings and enable semantic search.
`

	ingestResult, err := ragPipeline.IngestDocumentWithDebug(ctx, testContent, "test_document.md")
	if err != nil {
		log.Printf("Failed to ingest test document: %v", err)
	} else if ingestResult.Debug != nil {
		fmt.Println("Ingestion Debug Info:")
		debugJSON, _ := json.MarshalIndent(ingestResult.Debug, "", "  ")
		fmt.Printf("%s\n", string(debugJSON))
	}

	// Test RAG query with debug
	fmt.Println("\n--- Testing RAG Query with Debug ---")
	testQuery := "What is RAG?"

	queryResult, err := ragPipeline.RAGQueryWithDebug(ctx, testQuery)
	if err != nil {
		log.Printf("Failed to process query: %v", err)
	} else {
		fmt.Printf("Answer: %s\n", queryResult.Answer)
		if queryResult.Debug != nil {
			fmt.Println("\nQuery Debug Info:")
			debugJSON, _ := json.MarshalIndent(queryResult.Debug, "", "  ")
			fmt.Printf("%s\n", string(debugJSON))
		}
	}

	fmt.Println("\n=== Debug Mode Test Complete ===")
}

func testNormalMode() {
	fmt.Println("\n=== Testing Normal Mode ===")
	
	// Create a config with debug mode disabled
	cfg := &config.Config{
		OllamaAPIBaseURL:     "http://localhost:11434",
		OllamaEmbeddingModel: "qwen-embedding",
		OllamaGenerateModel:  "qwen3:1.7b",
		QdrantGRPCURL:        "localhost:6334",
		QdrantCollectionName: "rag_pipeline_go_normal_test",
		VectorSize:           1024,
		DebugMode:            false,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()

	// Initialize clients
	ollamaClient := ollama.NewClient(cfg)
	qdrantClient, err := qdrant.NewClient(ctx, cfg)
	if err != nil {
		log.Fatalf("Failed to initialize Qdrant client: %v", err)
	}

	// Initialize RAG pipeline
	ragPipeline := rag.NewRAGPipeline(
		ollamaClient,
		qdrantClient,
		cfg,
		512, // Smaller chunk size for testing
		64,  // Smaller overlap for testing
		2,   // Top K for retrieval
	)

	// Test document ingestion without debug
	fmt.Println("\n--- Testing Document Ingestion without Debug ---")
	testContent := `
# Test Document Normal Mode

This is a test document for normal mode operation.
No debug information should be displayed.
`

	err = ragPipeline.IngestDocument(ctx, testContent, "test_document_normal.md")
	if err != nil {
		log.Printf("Failed to ingest test document: %v", err)
	} else {
		fmt.Println("Document ingested successfully (no debug info)")
	}

	// Test RAG query without debug
	fmt.Println("\n--- Testing RAG Query without Debug ---")
	testQuery := "What is this document about?"

	answer, err := ragPipeline.RAGQuery(ctx, testQuery)
	if err != nil {
		log.Printf("Failed to process query: %v", err)
	} else {
		fmt.Printf("Answer: %s\n", answer)
		fmt.Println("(No debug info displayed)")
	}

	fmt.Println("\n=== Normal Mode Test Complete ===")
}

func main() {
	// Test both debug and normal modes
	testDebugMode()
	testNormalMode()
}
